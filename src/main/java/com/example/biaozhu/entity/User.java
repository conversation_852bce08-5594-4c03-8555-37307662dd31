package com.example.biaozhu.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 用户实体类
 */
@Entity
@Table(name = "users",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = "username"),
           @UniqueConstraint(columnNames = "email")
       })
@NoArgsConstructor
public class User {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 20)
    private String username;
    
    @NotBlank
    @Size(max = 50)
    @Email
    private String email;
    
    @NotBlank
    @Size(max = 120)
    private String password;
    
    @Size(max = 50)
    private String fullName;
    
    @Size(max = 200)
    private String bio;
    
    @Size(max = 20)
    private String phone;
    
    @Size(max = 255)
    private String avatarUrl;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "team_id")
    private Team team;
    
    @Size(max = 50)
    private String position;
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "user_roles",
               joinColumns = @JoinColumn(name = "user_id"),
               inverseJoinColumns = @JoinColumn(name = "role_id"))
    @JsonIgnoreProperties("users")
    private Set<Role> roles = new HashSet<>();
    
    private boolean active = true;
    
    @Size(max = 20)
    @Column(name = "status")
    private String status = "ACTIVE";
    
    @Column(name = "accuracy_rate")
    private Double accuracyRate = 0.0;
    
    @Column(name = "task_completed_count")
    private Integer taskCompletedCount = 0;
    
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at", updatable = false)
    private Date createdAt;
    
    @UpdateTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;
    
    private Date lastLoginAt;
    
    public User(String username, String email, String password) {
        this.username = username;
        this.email = email;
        this.password = password;
    }
    
    public User(String username, String email, String password, String fullName) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.fullName = fullName;
    }
    
    /**
     * 获取用户名称（为兼容UserPrincipal）
     * @return 用户全名
     */
    public String getName() {
        return this.fullName;
    }
    
    /**
     * 获取用户ID
     * @return 用户ID
     */
    public Long getId() {
        return id;
    }
    
    /**
     * 设置用户ID
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }
    
    /**
     * 获取用户名
     * @return 用户名
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * 设置用户名
     * @param username 用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
     * 获取电子邮件
     * @return 电子邮件
     */
    public String getEmail() {
        return email;
    }
    
    /**
     * 设置电子邮件
     * @param email 电子邮件
     */
    public void setEmail(String email) {
        this.email = email;
    }
    
    /**
     * 获取密码
     * @return 密码
     */
    public String getPassword() {
        return password;
    }
    
    /**
     * 设置密码
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password;
    }
    
    /**
     * 获取用户角色
     * @return 角色集合
     */
    public Set<Role> getRoles() {
        return roles;
    }
    
    /**
     * 设置用户角色
     * @param roles 角色集合
     */
    public void setRoles(Set<Role> roles) {
        this.roles = roles;
    }
    
    /**
     * 获取用户状态
     * @return 用户状态
     */
    public String getStatus() {
        return status;
    }
    
    /**
     * 设置用户状态
     * @param status 用户状态
     */
    public void setStatus(String status) {
        this.status = status;
    }
    
    /**
     * 获取准确率
     * @return 准确率
     */
    public Double getAccuracyRate() {
        return accuracyRate;
    }
    
    /**
     * 设置准确率
     * @param accuracyRate 准确率
     */
    public void setAccuracyRate(Double accuracyRate) {
        this.accuracyRate = accuracyRate;
    }
    
    /**
     * 获取已完成任务数量
     * @return 已完成任务数量
     */
    public Integer getTaskCompletedCount() {
        return taskCompletedCount;
    }
    
    /**
     * 设置已完成任务数量
     * @param taskCompletedCount 已完成任务数量
     */
    public void setTaskCompletedCount(Integer taskCompletedCount) {
        this.taskCompletedCount = taskCompletedCount;
    }

    /**
     * 重写hashCode方法，避免使用roles集合
     */
    @Override
    public int hashCode() {
        return Objects.hash(id, username, email);
    }

    /**
     * 重写equals方法，避免使用roles集合
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return Objects.equals(id, user.id) && 
               Objects.equals(username, user.username) && 
               Objects.equals(email, user.email);
    }

    // Lombok @Data注解已移除，手动添加其他getters和setters
    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Team getTeam() {
        return team;
    }

    public void setTeam(Team team) {
        this.team = team;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Date getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(Date lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }
} 